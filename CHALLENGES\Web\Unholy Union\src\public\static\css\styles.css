body {
    background-color: #061f2b;
    color: white;
    font-family: "Press Start 2P";
    /* font-weight: 200; */
    font-size: small;
    font-style: normal;
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

header h1 {
    color: #333;
    font-size: 32px;
}

.warning {
    color: #ff008d;
}

nav {
    background-color: #ea00d9;
}

div button {
    background-color: #ea00d9;
    color: white;
}

nav a {
    color: white;
}

header p {
    color: #666;
    line-height: 1.5;
}

.genres {
    font-weight: bold;
    color: #007bff;
}

.section-header {
    /* background-color: #130981; */
    /* color: ; */
    border: 2px solid white;
    padding: 7px;
    justify-content: center;
}

/* aa */

.content {
    flex: 1;
    padding: 20px;
    background-color: #f0f0f0;
}
.footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #091833;
    color: white;
    transition: height 0.3s;
    overflow: hidden;
}
.footer.collapsed {
    height: 40px;
}
.footer.expanded {
    height: 45%;
}

.footer.fullscreen {
    height: 100%;
}

.footer-sections {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
}
.footer-header {
    /* margin-left: 20px; */
    padding-left: 0.75rem !important;
    display: flex;
    /* justify-content: space-around; */
    align-items: center;
    width: 100%;
    background-color: #644b77;
    padding: 10px 0;
}
.footer-section-container {
    display: flex;
    justify-content: space-around;
    width: 100%;
    height: calc(100% - 40px);
}
.footer-section {
    width: 50%;
    padding: 10px;
    box-sizing: border-box;
}
.footer-section h4 {
    margin: 0;
    padding: 10px 0;
    background-color: #644b77;
    text-align: center;
}
.footer-section p {
    padding: 10px;
    background-color: #555;
    text-align: center;
    height: calc(100% - 40px);
    margin: 0;
}
h4 {
    margin: 0;
    padding: 0 20px;
    font-size: 0.7rem;
}

.footer-header button {
    display: flex; /* Enable Flexbox */
    align-items: center; /* Center vertically */
    justify-content: center; /* Center horizontally */
    width: 24px;
    height: 24px;
    padding: 0; /* Remove default padding */
    margin-right: 5px;
    border: none; /* Remove default border */
    /* background: transparent; Remove default background */
    cursor: pointer; /* Add pointer cursor on hover */
    border-radius: 50%; /* Make the button circular */
}

.footer-header svg {
    width: 12px; /* Set the SVG width */
    height: 12px; /* Set the SVG height */
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    border: none;
    background-color: none;
}
